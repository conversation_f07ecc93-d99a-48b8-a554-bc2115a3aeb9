@import "lenis.scss";
@import "variables.scss";
@import "fonts.scss";
@import "flipLink.scss";
@import "intro.scss";

a {
    text-decoration: none;
    color: $white;
}

img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.container {
    padding: 0 40px;
}

main {
    width: 100%;
    overflow-x: hidden;
    padding-bottom: 160px;
    display: flex;
    flex-direction: column;
    gap: 160px;
}

section {
    width: 100%;
    overflow-x: hidden;
}

.mT70 {
    margin-top: 70px;
}

.mT50 {
    margin-top: 50px;
}

.mT20 {
    margin-top: 20px;
}

// SplitText Animation
.splitText {
    overflow: hidden;

    span {
        display: inline-block;
        overflow: hidden;
        margin: 0px;

        span {
            display: inline-block;
            transform: translateY(120%);
        }
    }

    &.font12 {
        line-height: 110%;
    }

    &.font52 {
        line-height: 90%;
    }
}

@media (max-width: 600px) {
    main {
        padding-bottom: 100px;
        gap: 100px;
    }

    .container {
        padding: 0 15px;
    }

    .mT70 {
        margin-top: 40px;
    }

    .mT50 {
        margin-top: 28px;
    }

    .mT20 {
        margin-top: 14px;
    }
}