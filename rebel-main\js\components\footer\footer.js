import './footer.scss';

function CreateFooter() {
    const footer = document.querySelector('footer');

    if (!footer) {
        return;
    }

    footer.innerHTML = `
        <div class="container">
            <div class="top">
                <div class="left">
                    <h4 class="font36">Interested in collaborating with me?</h4>
                    <p class="font12">
                        Together, we have the power to shape and design the world we envision, transforming our shared ideas into
                        reality through innovative and creative solutions.
                    </p>
                </div>
                <div class="right">
                    <p class="font12">51° 30' 26.64'' N / 0° 7' 40.08'' W</p>
                    <p class="font12">Remote From England, UK</p>
                </div>
            </div>
            <div class="center">
                <div class="left">
                    <p class="font12">Drop a message</p>
                    <a href="mailto:<EMAIL>" class="flip__link">
                        <span class="flip__link--text"><EMAIL></span>
                        <span class="flip__link--text"><EMAIL></span>
                    </a>
                </div>
                <div class="right">
                    ${[
            { platform: 'Twitter', url: 'https://ui8.net/users/frontendzaid' },
            { platform: 'Facebook', url: 'https://ui8.net/users/frontendzaid' },
            { platform: 'Linkedin', url: 'https://ui8.net/users/frontendzaid' },
            { platform: 'Instagram', url: 'https://ui8.net/users/frontendzaid' },
            { platform: 'Behance', url: 'https://ui8.net/users/frontendzaid' },
            { platform: 'Dribble', url: 'https://dribbble.com/zaidkhan3419' }
        ].map(({ platform, url }) => `
                        <a href="${url}" class="flip__link" target="_blank" rel="noopener noreferrer">
                            <span class="flip__link--text">${platform}</span>
                            <span class="flip__link--text">${platform}</span>
                        </a>`).join('')}
                </div>
            </div>
            <div class="bottom">
                <p class="font12">
                    Copyright © 2024 Rebel Grace. All rights reserved.
                    <span>Design & Development by Zaid</span>
                </p>
            </div>
        </div>
        `;
};

CreateFooter();