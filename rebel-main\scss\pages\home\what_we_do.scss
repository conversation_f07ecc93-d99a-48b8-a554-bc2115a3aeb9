.what_we_do {
    .container {
        .wrapper {
            /* Row */
            .row {
                display: flex;
                align-items: flex-end;
                gap: 50px;
                border-bottom: 1px solid $borderColor;
                padding: 20px 0;

                .title {
                    width: 100%;

                    h5 {
                        color: $grey;
                    }

                    p {
                        margin-top: 10px;
                        max-width: 460px;
                        width: 100%;
                    }
                }

                /* Img */
                .img {
                    width: 100%;
                    height: 35vh;

                    img {
                        display: block;
                        clip-path: inset(0 100% 0 0);
                        transition: $transition;
                    }
                }

                @media (max-width: 1030px) {
                    .img {
                        max-height: 200px;
                    }
                }
            }

            @media (max-width: 600px) {
                .row {
                    align-items: flex-start;
                    flex-direction: column;
                    gap: 20px;
                }
            }
        }
    }
}