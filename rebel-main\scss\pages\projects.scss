.projects_all {
    padding-top: 100px;

    .container {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 40px;

        .case {
            display: block;
            width: 100%;

            .projectCase {
                height: 70vh;
            }

            .details {
                margin-top: 15px;

                p {
                    color: $grey;
                    margin-top: 2px;
                }
            }
        }
    }

    @media (max-width: 1030px) {
        .container {
            grid-template-columns: 1fr 1fr;

            .case {
                .projectCase {
                    max-height: 400px;
                }
            }
        }
    }

    @media (max-width: 600px) {
        .container {
            grid-template-columns: 1fr;

            .case {
                .projectCase {
                    max-height: 400px;
                }
            }
        }
    }
}