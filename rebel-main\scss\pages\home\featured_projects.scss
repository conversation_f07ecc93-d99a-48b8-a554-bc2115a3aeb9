.featured_projects {
    .container {

        /* Project Wrapper */
        .featured_projects_wrapper {

            /* Both rows */
            .row {
                .case {
                    display: block;

                    .details {
                        margin-top: 15px;

                        p {
                            color: $grey;
                            margin-top: 2px;
                        }
                    }
                }
            }

            /* First row */
            .row1 {
                display: flex;
                justify-content: space-between;

                .case {
                    width: 40%;

                    &:nth-child(1) {
                        .projectCase {
                            height: 70vh;
                        }
                    }

                    &:nth-child(2) {
                        .projectCase {
                            height: 110vh;
                        }
                    }
                }
            }

            /* Second row */
            .row2 {
                display: flex;
                justify-content: center;
                width: 100%;
                margin-top: 12vw;

                .case {
                    width: 40%;

                    .projectCase {
                        width: 100%;
                        height: 50vh;
                    }
                }
            }

            @media (max-width: 1030px) {
                .row1 {
                    flex-direction: column;
                    gap: 50px;

                    .case {
                        width: 60%;

                        &:nth-child(1) {
                            .projectCase {
                                max-height: 500px;
                            }
                        }

                        &:nth-child(2) {
                            margin-left: 10vw;

                            .projectCase {
                                max-height: 700px;
                            }
                        }
                    }
                }

                .row2 {
                    .case {
                        width: 100%;

                        .projectCase {
                            max-height: 600px;
                        }
                    }
                }
            }

            @media (max-width: 600px) {
                .row1 {
                    .case {
                        width: 100%;

                        &:nth-child(1) {
                            .projectCase {
                                max-height: auto;
                                height: auto;
                            }
                        }

                        &:nth-child(2) {
                            margin-left: 0vw;

                            .projectCase {
                                max-height: auto;
                                height: auto;
                            }
                        }
                    }
                }

                .row2 {
                    .case {
                        width: 100%;

                        .projectCase {
                            max-height: auto;
                            height: auto;
                        }
                    }
                }
            }
        }
    }
}