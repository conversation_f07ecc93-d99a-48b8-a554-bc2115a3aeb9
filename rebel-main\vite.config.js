// vite.config.js
import { resolve } from 'path'
import { defineConfig } from 'vite'

export default defineConfig({
    build: {
        rollupOptions: {
            input: {
                main: resolve(__dirname, 'index.html'),
                about: resolve(__dirname, 'about.html'),
                projects: resolve(__dirname, 'projects.html'),
                projectSingle: resolve(__dirname, 'projectsingle.html'),
                contact: resolve(__dirname, 'contact.html'),
                playground: resolve(__dirname, 'playground.html'),
            },
        },
    },
})