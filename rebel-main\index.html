<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="favicon.jpg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Rebel Grace</title>
  <!--===== SheryJS =====-->
  <link rel="stylesheet" href="https://unpkg.com/sheryjs/dist/Shery.css" />
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-9SV2DKQ6F7"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-9SV2DKQ6F7');
  </script>
</head>

<body>
  <!--===== display laoder from js component folder nav.js =====-->
  <!--===== display nav from js component folder nav.js =====-->

  <div class="overlay"></div>

  <div class="intro">
    <div class="intro_text">
      <div class="intro_text_heading">
        <h1>Rabel Grace</h1>
      </div>
      <div class="intro_text_p">
        <h2>UIUX Designer & Developer</h2>
      </div>
      <div class="intro_text_p mT20">
        <h2>Portfolio ©</h2>
      </div>
    </div>

    <img src="header_img_one.jpg" alt="girl">
  </div>

  <!--===== Main Content =====-->
  <main id="main">
    <!--===== Hero =====-->
    <section class="hero">
      <div class="hero_main">
        <!-- Hero Image -->
        <div class="heroimage">
          <img src="/header_img_one.jpg" alt="girl" />
          <img src="/header_img_two.jpg" alt="girl" />
        </div>

        <!-- Hero Heading -->
        <h1 class="fontVW">Rebel.Grace</h1>
      </div>

      <!-- Bio -->
      <div class="bio mT70">
        <div class="container">
          <!-- Dynamic data from js/data/bio.js-->
        </div>
      </div>
    </section>

    <!--=====  Section About ===== -->
    <section class="about">
      <div class="container">
        <!-- Text -->
        <div class="txt">
          <h2 class="font12 splitText">
            <span></span>Elevating businesses through crafting exceptional digital experiences, driving innovation,
            delivering
            impeccable design solutions, and enhancing success with expertise. My goal is to exceed expectations and set
            new standards in creative excellence.
          </h2>

          <h2 class="font12 mT50 splitText">
            At the core of my approach is a commitment to understanding each client's unique vision and translating it
            into a compelling digital presence. I specialize in creating user-centric designs that not only captivate
            audiences but also drive engagement and conversions. By leveraging cutting-edge technologies and staying
            ahead
            of design trends, I ensure that every project stands out in a competitive landscape. My passion for
            innovation
            and dedication to quality means I am always striving to deliver solutions that are not only aesthetically
            pleasing but also functionally superior, helping businesses achieve their goals and leave a lasting impact.
          </h2>
        </div>

        <!-- Numbers -->
        <div class="about_numbers mT50">
          <div class="block">
            <h3 class="fontNumber">+22</h3>
            <p class="font12 splitText">Total Projects</p>
          </div>
          <div class="block">
            <h3 class="fontNumber">+8</h3>
            <p class="font12 splitText">Years of Experience</p>
          </div>
        </div>
      </div>
    </section>

    <!--=====  Featured Projects ===== -->
    <section class="featured_projects">
      <div class="container">
        <!-- Heading -->
        <div class="heading">
          <h2 class="font52 splitText">
            Featured Projects
          </h2>

          <a href="projects.html" class="flip__link" id="logo">
            <span class="flip__link--text">View&nbsp;All&nbsp;Projects</span>
            <span class="flip__link--text">View&nbsp;All&nbsp;Projects</span>
          </a>
        </div>

        <!-- Wrapper -->
        <div class="featured_projects_wrapper mT70">
          <!-- Dynamic data from js/data/featuredProject.js -->
        </div>
      </div>
    </section>

    <!--===== Services ===== -->
    <section class="services">
      <div class="container">
        <!-- Heading -->
        <div class="heading">
          <h2 class="font52 splitText">Services</h2>
        </div>
        <!-- Wrapper -->
        <div class="wrapper mT70">
          <div class="txt">
            <p class="font12 splitText">
              From initial concept to final product delivery, my mission is to help businesses create exceptional
              experiences through top-tier design. While my primary expertise lies in UI/UX Design, my work encompasses
              a broad skill set, including visual identity and motion design.
            </p>
          </div>
          <!-- Accordians -->
          <div class="accordians_wrapper">
            <!-- Display data from js data folder services.js -->
          </div>
        </div>
      </div>
    </section>

    <!--=====  What we Do ===== -->
    <section class="what_we_do">
      <div class="container">
        <!-- heading -->
        <div class="heading">
          <h2 class="font52 splitText">Discover, Design, Develop</h2>
        </div>
        <!-- Wrapper -->
        <div class="wrapper">
          <!-- Dynamic Data from js/data/what_we_do.js -->
        </div>
      </div>
    </section>

    <!--===== Expertise ===== -->
    <section class="areasWork">
      <div class="container">
        <!-- heading -->
        <div class="heading">
          <h2 class="font52 splitText">Fields of Expertise</h2>
        </div>

        <!-- wrapper -->
        <div class="wrapper mT70">
          <!-- Categories -->
          <div class="categories">
            <p class="font12 splitText">HealthTech</p>
            <p class="font12 splitText">Supply Chain</p>
            <p class="font12 splitText">Research and Development</p>
            <p class="font12 splitText">Wellness</p>
            <p class="font12 splitText"> Blockchain and NFTs</p>
            <p class="font12 splitText">EdTech</p>
            <p class="font12 splitText">Data Insights</p>
            <p class="font12 splitText">Entertainment</p>
            <p class="font12 splitText">Finance</p>
            <p class="font12 splitText">Real Estate</p>
            <p class="font12 splitText">Travel & Hospitality</p>
            <p class="font12 splitText">Retail Tech</p>
            <p class="font12 splitText">Automotive</p>
            <p class="font12 splitText">Real Estate Tech</p>
            <p class="font12 splitText">Nonprofit</p>
            <p class="font12 splitText">Legal Tech</p>
            <p class="font12 splitText">Energy & Utilities</p>
            <p class="font12 splitText">Government</p>
            <p class="font12 splitText">Media & Publishing</p>
            <p class="font12 splitText">Telecommunications</p>
          </div>
          <!-- img -->
          <div class="areasImage">
            <img class="mainImage" src="about_header.jpg" alt="Fields of Expertise">
          </div>
        </div>
      </div>
    </section>
  </main>

  <!--===== Footer ===== -->
  <footer>
    <!-- display data from js components folder footer.js -->
  </footer>

  <!--===== Scripts =====-->
  <!-- Three.js is needed for 3d Effects -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/0.155.0/three.min.js"></script>
  <!-- ControlKit is needed for Debug Panel -->
  <script src="https://cdn.jsdelivr.net/gh/automat/controlkit.js@master/bin/controlKit.min.js"></script>
  <script type="text/javascript" src="https://unpkg.com/sheryjs/dist/Shery.js"></script>
  <!-- Custom -->
  <script type="module" src="js/main.js"></script>
</body>

</html>