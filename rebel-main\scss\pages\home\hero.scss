.hero {
    opacity: 0;
    .hero_main {
        position: relative;
        overflow: hidden;

        // Hero Image
        .heroimage {
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }

        h1 {
            position: absolute;
            bottom: -2vw;
            left: 0;
            mix-blend-mode: difference;
            text-align: center;
            pointer-events: none;
        }
    }

    /* BIO */
    .bio {
        .container {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .bio_wrapper {
                display: flex;
                align-items: center;
                gap: 30px;

                > div {
                    p {
                        &:nth-child(1) {
                            color: $grey;
                            margin-bottom: 5px;
                        }
                    }
                }
            }
        }

        @media (max-width: 1030px) {
            .container {
                flex-direction: column;
                align-items: flex-start;
                gap: 30px;

                .bio_wrapper {
                    width: 100%;
                    > div {
                        flex: 1;
                    }
                }
            }
        }

        @media (max-width: 600px) {
            .container {
                .bio_wrapper {
                    flex-direction: column;
                    align-items: flex-start;
                }
            }
        }
    }
}
