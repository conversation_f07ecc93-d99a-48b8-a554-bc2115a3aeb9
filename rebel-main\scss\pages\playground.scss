.playground_images {
    position: fixed;
    top: 52%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 85vh;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    @media (max-width: 1280px) {
        width: 70%;
    }

    @media (max-width: 1030px) {
        max-height: 600px;
        width: 80%;
    }

    @media (max-width: 600px) {
        width: 90%;
        max-height: 300px;
    }
}

.playgroundText {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 40px;
    max-width: 400px;
    color: #fff;
    mix-blend-mode: difference;

    @media (max-width: 600px) {
        left: 15px;
        top: 17%;
    }
}
