@font-face {
    font-family: Sregular;
    src: url(../SuisseIntl-Light.ttf);
}

@font-face {
    font-family: Smedium;
    src: url(../SuisseIntl-SemiBold.ttf);
}

@font-face {
    font-family: MMLight;
    src: url(../MartianMono-Light.ttf);
}

@font-face {
    font-family: MMMedium;
    src: url(../MartianMono-Medium.ttf);
}

.fontVW {
    font-size: 15.3vw;
    letter-spacing: -0.62vw;
    line-height: 100%;
    text-transform: uppercase;
    font-family: Smedium, sans-serif;
}

.fontNumber {
    font-size: 120px;
    font-family: Smedium, sans-serif;
    line-height: 100%;
    letter-spacing: -0.5px;

    @media (max-width: 600px) {
        font-size: 20vw;
    }
}

.font52 {
    font-size: 52px;
    font-family: Smedium, sans-serif;
    letter-spacing: 1px;
    text-transform: uppercase;

    @media (max-width: 600px) {
        font-size: 36px;
    }
}

.font36 {
    font-size: 36px;
    font-family: Smedium, sans-serif;
    letter-spacing: 1px;
    text-transform: uppercase;

    @media (max-width: 600px) {
        font-size: 24px;
    }
}

.font12 {
    font-size: 12px;
    letter-spacing: -0.05px;
    font-weight: lighter;
    text-transform: uppercase;
    line-height: 140%;
}
