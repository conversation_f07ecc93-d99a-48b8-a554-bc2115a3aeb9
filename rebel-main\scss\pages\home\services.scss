.services {
    .container {
        .wrapper {
            display: flex;
            gap: 50px;

            .txt {
                flex: 1;

                p {
                    color: $grey;
                    max-width: 420px;
                    width: 100%;
                }
            }

            /* Accordian wrapper */
            .accordians_wrapper {
                flex: 1;

                .accordion {
                    color: $white;
                    padding: 20px 0;
                    overflow: hidden;
                    cursor: pointer;
                    border-bottom: 1px solid $borderColor;

                    .question {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        gap: 20px;

                        .close {
                            transform: rotate(-45deg);
                            transition: $transition;

                            img {
                                width: 28px;
                            }

                            &.rotate {
                                transform: rotate(0);
                            }
                        }
                    }

                    .answer {
                        height: 0;
                        opacity: 0;
                        overflow: hidden;
                        transition: $transition;
                        width: 90%;

                        .tags {
                            p {
                                color: $grey;
                            }
                        }
                    }
                }
            }

            @media (max-width: 600px) {
                flex-direction: column;
                gap: 30px;

                .accordians_wrapper {
                    .accordion {
                        padding: 14px 0;

                        .question {
                            .close {
                                img {
                                    width: 20px;
                                }
                            }
                        }

                        .answer {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }
}