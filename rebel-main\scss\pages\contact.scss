.contact_header {
    padding-top: 100px;

    .container {
        .top {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;

            h1 {
                max-width: 620px;
                width: 100%;
            }

            lord-icon {
                height: 50px;
                width: 50px;
            }

            @media (max-width: 600px) {
                flex-direction: column;
                align-items: flex-start;
                gap: 20px;
            }
        }

        .text {
            p {
                max-width: 500px;
                width: 100%;
                color: $grey;
            }
        }
    }
}

.mail {
    display: flex;
    justify-content: center;
    overflow: hidden;

    a {
        font-size: 5vw;
        letter-spacing: -.5vw;
        position: relative;

        &::before,
        &::after {
            position: absolute;
            content: "";
            left: 0;
            bottom: 0;
            display: block;
            width: 100%;
            height: 2px;
            background: $white;
            transition: $transition;
        }

        &::before {
            transform: scaleX(0);
            transform-origin: left;
        }

        &::after {
            transform-origin: right;
            transition-delay: .25s;
        }

        &:hover {
            &::before {
                transform: scaleX(1);
                transition-delay: .25s;
            }

            &::after {
                transform: scaleX(0);
                transition-delay: 0s;
            }
        }
    }

    @media (max-width: 600px) {
        a {
            font-size: 7vw;
        }
    }
}