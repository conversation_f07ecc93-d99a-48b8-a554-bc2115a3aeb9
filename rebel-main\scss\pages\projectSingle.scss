/* Header */
.projectSingle_header {
    padding-top: 100px;
    overflow: hidden;

    .container {
        .projectSingle_heading {
            p {
                color: $grey;
            }
        }
    }

    .projectSingle_header_img {
        width: 100%;
        overflow: hidden;

        img {
            transform: scale(1.5);
        }

        @media (max-width: 600px) {
            height: 50vh;
        }
    }
}

/* Texts */
.projectSingle_txt {
    overflow: hidden;
    .container {
        p {
            max-width: 600px;
            width: 100%;
        }

        a {
            width: fit-content;
        }
    }
}

/* images */
.image_holder {
    width: 100%;
    overflow: hidden;

    img {
        transform: scale(1.5);
    }

    @media (max-width: 600px) {
        height: 50vh;
    }
}
