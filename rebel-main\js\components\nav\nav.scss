@import "../../../scss/utilities/variables.scss";

nav {
    width: 100%;
    padding: 32px 0;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 50;

    .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 6;

        .nav_links {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        a {
            font-size: calc(12 / 14.4 * 1rem);
        }

        /* Hamburger */
        .hamburger {
            display: none;
            width: 45px;

            > div {
                width: 100%;
                height: 1px;
                background: $white;
                transition: all 0.3s ease;

                &:nth-child(2) {
                    margin-top: 15px;
                }
            }

            &.active {
                > div {
                    width: 80%;
                    height: 2px;
                    background: $white;

                    &:nth-child(1) {
                        transform: rotate(45deg) translateY(3px) translateX(4px);
                    }

                    &:nth-child(2) {
                        transform: rotate(-45deg) translateY(-9px) translateX(8px);
                    }
                }
            }
        }
    }

    @media (max-width: 1030px) {
        .container {
            .nav_links {
                display: none;
            }
            .hamburger {
                display: block;
            }
        }
    }
}

// For responsive nav
.sliding_navigation {
    position: fixed;
    top: 0px;
    left: 0;
    width: 100%;
    background: rgba(36, 36, 36, 0.5);
    z-index: 9;
    backdrop-filter: blur(10px);
    padding: 100px 40px 40px 40px;
    border-bottom: 1px solid $borderColor;
    transform: translateY(-100%);
    transition: all 1.5s cubic-bezier(0.19, 1, 0.22, 1);
    visibility: hidden;

    &.active {
        transform: translateY(0);
        visibility: visible;
    }

    a {
        font-size: 32px;
        margin-top: 10px;
    }

    @media (max-width: 600px) {
        padding: 100px 15px 30px 15px;
    }
}

// overlay
.overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 8;
    top: 0;
    left: 0;
    display: none;

    &.active {
        display: block;
    }
}

// For Nav Blur
.navBlur {
    position: absolute;
    top: 0;
    left: 0;
    height: 200%;
    width: 100%;
    pointer-events: none;
    z-index: 5;
    transform: translateZ(0) translateY(-30%);

    > div {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;

        &:nth-child(1) {
            z-index: 2;
            -webkit-backdrop-filter: blur(1px);
            backdrop-filter: blur(1px);
            -webkit-mask: linear-gradient(
                to top,
                rgba(255, 255, 255, 0) 0%,
                rgb(255, 255, 255) 12.5%,
                rgb(255, 255, 255) 37.5%,
                rgba(255, 255, 255, 0) 50%
            );
        }

        &:nth-child(2) {
            z-index: 3;
            -webkit-backdrop-filter: blur(2px);
            backdrop-filter: blur(2px);
            -webkit-mask: linear-gradient(
                to top,
                rgba(255, 255, 255, 0) 12.5%,
                rgb(255, 255, 255) 37.5%,
                rgb(255, 255, 255) 50%,
                rgba(255, 255, 255, 0) 62.5%
            );
        }

        &:nth-child(3) {
            z-index: 4;
            webkit-backdrop-filter: blur(4px);
            backdrop-filter: blur(4px);
            -webkit-mask: linear-gradient(
                to top,
                rgba(255, 255, 255, 0) 37.5%,
                rgb(255, 255, 255) 50%,
                rgb(255, 255, 255) 62.5%,
                rgba(255, 255, 255, 0) 75%
            );
        }

        &:nth-child(4) {
            z-index: 5;
            -webkit-backdrop-filter: blur(8px);
            backdrop-filter: blur(8px);
            -webkit-mask: linear-gradient(
                to top,
                rgba(255, 255, 255, 0) 50%,
                rgb(255, 255, 255) 62.5%,
                rgb(255, 255, 255) 75%,
                rgba(255, 255, 255, 0) 87.5%
            );
        }
    }
}