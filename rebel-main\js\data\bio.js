const bioData = [
    {
        currently: "Currently",
        role: "Development Head at NovaBitz"
    },
    {
        currently: "Freelancer",
        role: "<PERSON><PERSON><PERSON> & others"
    },
    {
        currently: "Specialized at",
        role: "UI/UX, Development & Branding"
    },
    {
        currently: "Empowering",
        role: "Website Development"
    },
    {
        currently: "Enthusiastic by",
        role: "Digital, Art & Technology"
    },
    {
        currently: "From",
        role: "UK, England"
    }
];

export function fetching_Bio_Data() {
    const bioContainer = document.querySelector('.bio .container');

    if (bioContainer) {
        const leftColumn = bioData.slice(0, 3).map(item => `
            <div>
                <p class="font12 splitText">${item.currently}</p>
                <p class="font12 splitText">${item.role}</p>
            </div>
        `).join('');

        const rightColumn = bioData.slice(3).map(item => `
            <div>
                <p class="font12 splitText">${item.currently}</p>
                <p class="font12 splitText">${item.role}</p>
            </div>
        `).join('');

        bioContainer.innerHTML = `
            <div class="bio_wrapper">${leftColumn}</div>
            <div class="bio_wrapper">${rightColumn}</div>
        `;
    }
}