// JSON data
const data = [
    {
        "title": "Research",
        "text": "Through in-depth research, I aim to fully grasp the project's objectives and requirements. I develop a visual moodboard to lay the groundwork for typography, layout, motion, and color, ensuring a seamless blend of functionality and user engagement.",
        "imgSrc": "whatWeDo1.png",
        "alt": "Research"
    },
    {
        "title": "Design",
        "text": "Once the research is complete, I transition to the design phase. Here, I dive into design exploration, crafting concepts, developing brand identity, ensuring visual consistency, and planning for scalability.",
        "imgSrc": "whatWeDo2.jpg",
        "alt": "Design"
    },
    {
        "title": "Creation",
        "text": "As the design takes shape, I shift into the creation phase, where dynamic feedback drives the development of an engaging product. Here, I incorporate motion and interactivity to bring aesthetics to life, blending clear intent with a memorable user experience.",
        "imgSrc": "whatWeDo3.png",
        "alt": "Creation"
    },
    {
        "title": "Creative Development",
        "text": "After the creation phase, it's time for the handoff. This stage is where the final touches are meticulously added, and essential documentation is created. I maintain close collaboration with creative developers to bring the envisioned interaction and experience to life with precision and dedication, transforming the ordinary into the extraordinary.",
        "imgSrc": "whatWeDo4.jpg",
        "alt": "Creative Development"
    }
];

export function fetch_row_whatWeDo() {
    // Get the wrapper element
    const wwdWrapper = document.querySelector('.what_we_do .wrapper');
    if (!wwdWrapper) {
        return;
    }

    // Generate HTML dynamically
    wwdWrapper.innerHTML = data.map(item => `
    <div class="row">
        <div class="title">
            <h5 class="font12 splitText">${item.title}</h5>
            <p class="font12 splitText">${item.text}</p>
        </div>
        <div class="img">
            <img src="${item.imgSrc}" alt="${item.alt}">
        </div>
    </div>
    `).join('');
}