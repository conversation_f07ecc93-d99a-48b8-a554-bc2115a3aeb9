// Import Lenis
import Lenis from 'lenis';
// Import Gsap
import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

// Installing Lennis
const initSmoothScrolling = () => {
    const lenis = new Lenis({ lerp: 0.2 });
    lenis.on('scroll', ScrollTrigger.update);
    gsap.ticker.add(time => {
        lenis.raf(time * 1000);
    });
    gsap.ticker.lagSmoothing(0);
};

// SplitText Animation
const splitTextAnimation = () => {
    function splitText() {
        document.querySelectorAll('.splitText').forEach(element => {
            const words = element.textContent.trim().split(/\s+/);
            const spanWords = words.map(word => `<span><span>${word}</span></span>`).join(' ');
            element.innerHTML = spanWords;
        });
    }
    splitText();

    // Animate the split text
    gsap.utils.toArray(".splitText").forEach((textBlock) => {
        gsap.to(textBlock.querySelectorAll("span span"), {
            y: 0,
            duration: 1,
            stagger: 0.01,
            ease: "power3.out",
            scrollTrigger: {
                trigger: textBlock,
                start: "top 80%",
                toggleActions: "play none none none"
            }
        });
    });
}

// ====================================== Home page animations
// about section numbers
const animateNumbering = () => {
    document.querySelectorAll('.fontNumber').forEach(h3 => {
        const endValue = parseInt(h3.textContent.replace(/[^\d]/g, ''));
        gsap.timeline({ scrollTrigger: { trigger: h3, start: "top bottom", toggleActions: "play none none none" }, defaults: { duration: 2, ease: "power1.inOut" } })
            .fromTo(h3, { innerText: 0 }, {
                innerText: endValue, roundProps: "innerText", ease: "power3.inOut",
                onUpdate: () => h3.textContent = '+' + Math.ceil(gsap.getProperty(h3, "innerText")),
                onComplete: () => h3.textContent = '+' + endValue
            });
    });
}

// animate what we do image
const animateImage_whatwedo = () => {
    gsap.utils.toArray(".what_we_do .wrapper .row .img img").forEach((img) => {
        gsap.to(img, {
            clipPath: "inset(0 0 0 0)",
            ease: "none",
            scrollTrigger: {
                trigger: img,
                start: "top 80%",
                end: "top 50%",
                scrub: 1,
                onUpdate: (self) => {
                    gsap.to(img, {
                        clipPath: `inset(0 ${100 - self.progress * 100}% 0 0)`,
                        duration: 0.1, // A short duration to ensure it feels smooth
                        ease: "none"
                    });
                }
            }
        });
    });
}

// animate service accordians
const animateService = () => {
    const serviceAccordian = document.querySelectorAll(".accordians_wrapper .accordion");
    if (!serviceAccordian) {
        return;
    }

    serviceAccordian.forEach(item => {
        gsap.fromTo(item,
            { x: '35%', opacity: 0 },
            {
                x: '0%',
                opacity: 1,
                duration: 1,
                ease: 'none',
                scrollTrigger: {
                    trigger: item,
                    start: 'top 90%',
                    end: 'top 70%',
                    scrub: 1.4
                }
            }
        );
    });
}

// ====================================== About page animations
const animateImage_About = () => {
    const aboutHeaderImg = document.querySelector('.about_header_img img');
    if (aboutHeaderImg) {
        gsap.to(aboutHeaderImg, {
            scale: 1.2, // or whatever scale value you want
            scrollTrigger: {
                trigger: '.about_header_img',
                start: 'top center',
                end: 'bottom center',
                scrub: true
            }
        });
    }
}

const mouseMoveImage = () => {
    const rows = document.querySelectorAll('#careerBlocks .row');

    if (rows.length > 0) {
        const overlay = document.createElement('div');
        overlay.className = 'image-overlay';
        document.body.appendChild(overlay);

        const gsapOptions = { duration: 0.5, ease: 'power3.out' };

        rows.forEach(row => {
            row.addEventListener('mouseenter', () => {
                overlay.style.backgroundImage = `url(${row.getAttribute('data-image')})`;
                gsap.to(overlay, { scale: 1, opacity: 1, ...gsapOptions });
            });

            row.addEventListener('mousemove', e => {
                gsap.to(overlay, { x: e.clientX - 100, y: e.clientY - 100, duration: 0.3, ease: 'power3.out' });
            });

            row.addEventListener('mouseleave', () => {
                gsap.to(overlay, { scale: 0, opacity: 0, ...gsapOptions });
            });
        });
    }
}

// ====================================== Projects Single page animations
const animateImage_ProjectSingle = () => {
    const projectSingleImage = document.querySelector('.projectSingle_header_img img');
    if (projectSingleImage) {
        gsap.to(projectSingleImage, {
            scale: 1.2, // or whatever scale value you want
            scrollTrigger: {
                trigger: '.projectSingle_header_img',
                start: 'top center',
                end: 'bottom center',
                scrub: true
            }
        });
    }
}

const animateMultipleImage_ProjectSingle = () => {
    const projectSingleMultipleImages = document.querySelectorAll('.image_holder img');
    if (projectSingleMultipleImages.length) {
        projectSingleMultipleImages.forEach((image) => {
            gsap.to(image, {
                scale: 1, // or whatever scale value you want
                scrollTrigger: {
                    trigger: image.closest('.image_holder'), // Trigger based on the closest .image_holder
                    start: 'top center',
                    end: 'bottom center',
                    scrub: true
                }
            });
        });
    }
}

// Install All gsap functions
export function init() {
    initSmoothScrolling();
    splitTextAnimation();
    animateNumbering();
    animateImage_whatwedo();
    animateService();
    animateImage_About();
    mouseMoveImage();
    animateImage_ProjectSingle();
    animateMultipleImage_ProjectSingle();

    // Refresh ScrollTrigger to recalculate the triggers
    ScrollTrigger.refresh();
}

// intro animation for home page
export function createIntroAnimation() {
    const introElement = document.querySelector('.intro');

    // Check if the .intro element exists on the page
    if (!introElement) {
        return; // Exit the function if the element is not present
    }

    const tl = gsap.timeline({ defaults: { ease: 'power4.inOut' } });

    tl.set('.intro img', {
        // clipPath: 'inset(100% 0 0 0)'
    });

    tl.from('.intro_text_heading h1', {
        duration: 0.7,
        y: "100%",
    });

    tl.from('.intro_text_p h2', {
        duration: 1,
        y: "100%",
        delay: 0.2,
        stagger: 0.2
    }, "-=0.5");

    tl.from('.intro img', {
        duration: 1.5,
        clipPath: 'inset(100% 0 0 0)',
    }, "-=1.5");

    tl.to('.intro_text_heading h1', {
        duration: 0.7,
        y: "-100%",
    }, "+=1.5");

    tl.to('.intro_text_p h2', {
        duration: 1,
        y: "-100%",
        stagger: 0.2
    }, "-=.5");

    tl.to('.intro img', {
        duration: 1.5,
        clipPath: 'inset(0 0 100% 0)',
        ease: 'power4.inOut',
    }, "-=0.5");

    tl.to('.intro', {
        duration: 1.5,
        y: '-100%',
        ease: 'power4.inOut',
    }, "-=0.5");

    tl.to('.hero', {
        duration: 1,
        opacity: 1,
        ease: 'power4.inOut',
    }, "-=.5");

    tl.from('.hero h1', {
        duration: 1.5,
        y: '80%',
        opacity: 0,
        ease: 'power4.inOut',
        skewY: 1,
    }, "-=1");

    tl.call(() => {
        document.querySelector('.intro').style.display = 'none';
    });
}